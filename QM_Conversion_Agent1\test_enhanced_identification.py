#!/usr/bin/env python3
"""
Test script for enhanced error identification system
"""

import sys
import os
sys.path.append('.')

from nodes.conversion_nodes import ErrorInfoExtractor, StatementPositionMapper, PositionBasedIdentifier

def test_enhanced_identification():
    """Test the enhanced error identification system with real error data."""
    
    # Test with the actual error from the Excel files
    error_message = '''SQL Error [42601]: ERROR: mismatched parentheses at or near ")"
  Position: 2013'''

    print("=== Enhanced Error Information Extraction ===")
    error_info = ErrorInfoExtractor.extract_position_info(error_message)
    for key, value in error_info.items():
        print(f"{key}: {value}")

    print("\n=== Testing with Sample Statements ===")
    
    # Sample target code that contains the error
    target_code = '''LV_UPDATEDBY := TRIM((
        CASE WHEN (
            SELECT
                unnest(xpath('//LoginID/text(', LV_UPDATEACK)))::text)::text = '' THEN
            NULL
        ELSE
            (
                SELECT
                    unnest(xpath('//LoginID/text(', LV_UPDATEACK)))::text)::text
        END);'''

    # Sample statements for testing
    statements = [
        "LV_COMPANYID := TRIM((",
        '''LV_UPDATEDBY := TRIM((
        CASE WHEN (
            SELECT
                unnest(xpath('//LoginID/text(', LV_UPDATEACK)))::text)::text = '' THEN
            NULL
        ELSE
            (
                SELECT
                    unnest(xpath('//LoginID/text(', LV_UPDATEACK)))::text)::text
        END);''',
        "SAVEPOINT S_SAVEPOINT;"
    ]

    # Test enhanced position mapping
    print("\n=== Enhanced Position Mapping ===")
    positions = StatementPositionMapper.map_statements_to_positions(target_code, statements)
    for pos in positions:
        print(f"Statement #{pos['statement_number']}: {pos['match_method']} (confidence: {pos['confidence']:.2f})")
        print(f"  Position: {pos['start_position']}-{pos['end_position']}")
        print(f"  Content preview: {pos['content'][:50]}...")
        print()

    # Test enhanced identification
    print("=== Enhanced Error Identification ===")
    identification = PositionBasedIdentifier.identify_error_statement(error_info, positions)
    print(f"Identified Statement: #{identification['statement_number']}")
    print(f"Method: {identification['method']}")
    print(f"Confidence: {identification['confidence']:.2f}")
    print(f"Reasoning: {identification['reasoning']}")
    print(f"Verification: {identification['verification']}")
    print(f"Fallback Used: {identification['fallback_used']}")
    
    if identification.get('error_analysis'):
        print(f"Error Analysis: {identification['error_analysis']}")
    
    if identification.get('candidates'):
        print(f"\nAll Candidates ({len(identification['candidates'])}):")
        for i, candidate in enumerate(identification['candidates'][:3]):  # Show top 3
            print(f"  {i+1}. Statement #{candidate['statement_number']}: {candidate['method']} "
                  f"(confidence: {candidate['confidence']:.2f})")
            print(f"     {candidate['details']}")

def test_syntax_pattern_detection():
    """Test the syntax pattern detection specifically."""
    print("\n" + "="*60)
    print("=== Testing Syntax Pattern Detection ===")
    
    # Test statements with various syntax issues
    test_statements = [
        {
            'content': "SELECT unnest(xpath('//LoginID/text(', xml_data))",
            'expected_issues': ['Incomplete XPath expression detected']
        },
        {
            'content': "SELECT (col1, col2 FROM table",
            'expected_issues': ['Parentheses mismatch: 1 open, 0 close']
        },
        {
            'content': "SELECT 'unclosed string FROM table",
            'expected_issues': ['Unclosed quotes detected']
        },
        {
            'content': "SELECT col1, col2 FROM table",
            'expected_issues': []
        }
    ]
    
    for i, test_case in enumerate(test_statements, 1):
        print(f"\nTest Case {i}: {test_case['content'][:40]}...")
        
        # Create mock position data
        positions = [{
            'statement_number': i,
            'content': test_case['content'],
            'start_position': 0,
            'end_position': len(test_case['content'])
        }]
        
        # Test syntax pattern identification
        candidates = PositionBasedIdentifier._syntax_pattern_identification(positions, {})
        
        if candidates:
            candidate = candidates[0]
            print(f"  Detected Issues: {candidate['analysis']['syntax_issues']}")
            print(f"  Confidence: {candidate['confidence']:.2f}")
            
            # Check if detected issues match expected
            detected_issues = candidate['analysis']['syntax_issues']
            expected_issues = test_case['expected_issues']
            
            if not expected_issues and not detected_issues:
                print("  ✅ Correctly detected no issues")
            elif any(expected in str(detected_issues) for expected in expected_issues):
                print("  ✅ Correctly detected expected issues")
            else:
                print(f"  ❌ Expected: {expected_issues}, Got: {detected_issues}")
        else:
            if not test_case['expected_issues']:
                print("  ✅ Correctly detected no issues")
            else:
                print(f"  ❌ Expected issues but none detected: {test_case['expected_issues']}")

def test_real_world_scenario():
    """Test with the actual problematic code from the project."""
    print("\n" + "="*60)
    print("=== Testing Real World Scenario ===")
    
    # Read the actual updated target code
    try:
        with open('output_files/updated_target_code_iteration_1.sql', 'r') as f:
            real_target_code = f.read()
        
        # The actual error message
        real_error = '''PostgreSQL Error: ERROR:  mismatched parentheses at or near ")"
LINE 56:         END);
                    ^'''
        
        print("Testing with real target code and error message...")
        
        # Extract error info
        error_info = ErrorInfoExtractor.extract_position_info(real_error)
        print(f"Error Type: {error_info['error_type']}")
        print(f"Error Category: {error_info['error_category']}")
        print(f"Position: {error_info['position']}")
        print(f"Line: {error_info['line']}")
        print(f"All Quoted Texts: {error_info['all_quoted_texts']}")
        print(f"Suggested Fix: {error_info['suggested_fix']}")
        
        # For this test, we'll use a simplified statement list
        # In practice, this would come from the statement splitter
        problem_statements = [
            "LV_COMPANYID := TRIM((",
            '''LV_UPDATEDBY := TRIM((
        CASE WHEN (
            SELECT
                unnest(xpath('//LoginID/text(', LV_UPDATEACK)))::text)::text = '' THEN
            NULL
        ELSE
            (
                SELECT
                    unnest(xpath('//LoginID/text(', LV_UPDATEACK)))::text)::text
        END);''',
            "SAVEPOINT S_SAVEPOINT;"
        ]
        
        # Test position mapping
        positions = StatementPositionMapper.map_statements_to_positions(real_target_code, problem_statements)
        
        # Test identification
        identification = PositionBasedIdentifier.identify_error_statement(error_info, positions)
        
        print(f"\nFinal Identification:")
        print(f"  Statement: #{identification['statement_number']}")
        print(f"  Method: {identification['method']}")
        print(f"  Confidence: {identification['confidence']:.2f}")
        print(f"  Verification: {identification['verification']}")
        
        # Check if it correctly identified the XPath issue
        if identification['statement_number'] == 2:  # The problematic statement
            print("  ✅ Correctly identified the problematic statement!")
        else:
            print("  ❌ Did not identify the correct statement")
            
    except FileNotFoundError:
        print("Real target code file not found, skipping real-world test")

if __name__ == "__main__":
    print("Testing Enhanced Error Identification System")
    print("=" * 60)
    
    test_enhanced_identification()
    test_syntax_pattern_detection()
    test_real_world_scenario()
    
    print("\n" + "="*60)
    print("Testing Complete!")
