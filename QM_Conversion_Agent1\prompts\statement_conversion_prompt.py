"""
Prompts for statement conversion in database conversion.
"""
from typing import Dict

def create_statement_conversion_prompt(source_context: Dict, target_error_context: Dict, error_message: str, target_statements: list = None, previous_feedback: str = None) -> str:
    """
    Creates a focused prompt for fixing only the specific deployment error.

    This function creates a prompt that instructs the LLM to fix ONLY the specific
    deployment error mentioned in the error message, without over-correcting other issues.

    Args:
        source_context: Dictionary containing the source context (before, error, after statements)
        target_error_context: Dictionary containing the target error context
        error_message: The error message from deployment
        target_statements: List of target statements for context
        previous_feedback: Previous feedback if any

    Returns:
        A focused prompt string for the LLM
    """
    # Check if this is a target database-specific scenario (no source mapping)
    is_target_specific = (source_context.error_statement_number == 0 or
                         not source_context.error_statement or
                         source_context.error_statement.strip() == "")

    # Add feedback section if provided
    feedback_section = ""
    if previous_feedback:
        feedback_section = f"""
PREVIOUS CONVERSION FEEDBACK:
The previous conversion attempt was rejected with this feedback:
{previous_feedback}

Please address these specific issues:
- Fix the exact problems mentioned in the feedback
- Focus only on the deployment error, not other issues
- Ensure proper target database syntax for the specific error
- {"Apply target database expertise directly" if is_target_specific else "Use source context as reference"}

"""

    if is_target_specific:
        # Target database-specific conversion prompt (focused on specific error only)
        return f"""🚨 FOCUSED ERROR FIXING APPROACH 🚨
You are a PostgreSQL Expert. Fix ONLY the specific deployment error mentioned in the error message.

CRITICAL RULES:
- Fix ONLY the specific error mentioned in the error message
- Do NOT fix other potential issues unless they are causing the same error
- Preserve all original names exactly as they appear
- Provide only executable PostgreSQL code

{feedback_section}

ERROR MESSAGE:
{error_message}

TARGET ERROR CONTEXT:
Before Error (#{target_error_context.before_statement_number}):
{target_error_context.before_statement}

Error Statement (#{target_error_context.error_statement_number}):
{target_error_context.error_statement}

After Error (#{target_error_context.after_statement_number}):
{target_error_context.after_statement}

FOCUSED ANALYSIS APPROACH:
1. **Use the error position** (if provided) to locate the exact problematic syntax in the statement
2. **Identify the specific syntax causing the error** from the error message
3. **Apply minimal fix** to resolve only that specific syntax issue
4. **If the EXACT SAME syntax error appears multiple times**, fix ALL instances of that exact syntax only
5. **DO NOT fix SIMILAR but different issues** - only fix the exact syntax mentioned in the deployment error
6. **Preserve all other code** exactly as it is, including similar-looking but different syntax

TASK:
SURGICAL FIX APPROACH - Fix ONLY the exact syntax issue mentioned in the deployment error message.

CRITICAL INSTRUCTIONS:
1. **IDENTIFY**: Find the exact problematic syntax from the error message
2. **LOCATE**: Use position information to find the exact location in the statement
3. **REPLACE**: Replace ONLY that specific syntax with valid PostgreSQL syntax
4. **PRESERVE**: Keep everything else EXACTLY as it is, including other potential issues
5. **NO OVER-CORRECTION**: Do not fix other issues even if they exist in the same statement

OUTPUT FORMAT (JSON):
{{
  "corrected_statements": [
    {{
      "statement_number": <integer>,
      "original_statement": "<original statement>",
      "corrected_statement": "<corrected statement>",
      "statement_type": "before_error|error_statement|after_error",
      "changes_made": "<description of the specific change made to fix the deployment error>"
    }}
  ],
  "explanation": "<brief explanation of what specific error was fixed and how>"
}}

IMPORTANT:
- Fix ONLY the specific error type mentioned in the deployment error message
- If the same error pattern appears multiple times in the statement, fix ALL instances of that pattern
- DO NOT fix different types of issues - only the specific error type from the deployment error
- Keep before/after statements unchanged (set corrected_statement = original_statement)
- For error statement: Apply minimal fix to resolve the specific error type only"""

    else:
        # Standard conversion prompt (with source reference) - focused approach
        return f"""🚨 FOCUSED ERROR FIXING APPROACH 🚨
You are an Oracle to PostgreSQL Migration Expert. Fix ONLY the specific deployment error mentioned in the error message.

CRITICAL RULES:
- Fix ONLY the specific error mentioned in the error message
- Do NOT fix other potential issues unless they are causing the same error
- Preserve all original names exactly as they appear
- Use source context to understand the intended functionality
- Provide only executable PostgreSQL code

{feedback_section}

ERROR MESSAGE: {error_message}

SOURCE CONTEXT (Oracle):
Before Error (#{source_context.before_statement_number}): {source_context.before_statement}
Error Statement (#{source_context.error_statement_number}): {source_context.error_statement}
After Error (#{source_context.after_statement_number}): {source_context.after_statement}

TARGET CONTEXT (PostgreSQL with error):
Before Error (#{target_error_context.before_statement_number}): {target_error_context.before_statement}
Error Statement (#{target_error_context.error_statement_number}): {target_error_context.error_statement}
After Error (#{target_error_context.after_statement_number}): {target_error_context.after_statement}

FOCUSED ANALYSIS APPROACH:
1. **Use the error position** (if provided) to locate the exact problematic syntax in the statement
2. **Identify the specific syntax causing the error** from the error message
3. **Reference the source context** to understand the intended functionality
4. **Apply minimal PostgreSQL fix** to resolve only that specific syntax issue
5. **Preserve all other code** exactly as it is, including similar-looking but different syntax

TASK:
SURGICAL FIX APPROACH - Fix ONLY the exact syntax issue mentioned in the deployment error message.

CRITICAL INSTRUCTIONS:
1. **IDENTIFY**: Find the exact problematic syntax from the error message
2. **LOCATE**: Use position information to find the exact location in the statement
3. **REPLACE**: Replace ONLY that specific syntax with valid PostgreSQL syntax
4. **PRESERVE**: Keep everything else EXACTLY as it is, including other potential issues
5. **NO OVER-CORRECTION**: Do not fix other issues even if they exist in the same statement

SPECIFIC ERROR ANALYSIS:
1. **Use error position** (if provided) to pinpoint the exact problematic syntax
2. **Identify the exact syntax causing the error** from the error message
3. **Apply minimal PostgreSQL fix** to resolve only that specific syntax issue
4. **If the EXACT SAME syntax error appears multiple times**, fix ALL instances of that exact syntax only
5. **DO NOT fix DIFFERENT syntax issues** - only fix the exact syntax mentioned in the deployment error
6. **Preserve all other code** exactly as it is, including similar-looking but different syntax

CRITICAL DISTINCTION:
- ✅ **EXACT SAME ERROR**: Fix only the exact syntax issue mentioned in the error message
- ❌ **SIMILAR BUT DIFFERENT**: Do NOT fix similar-looking issues that are not the exact error mentioned
- **RULE**: Fix only the specific syntax/location mentioned in the deployment error message
- **FOCUS**: Target the exact problematic syntax from the error message, not similar patterns

OUTPUT FORMAT (JSON):
{{
  "corrected_statements": [
    {{
      "statement_number": <integer>,
      "original_statement": "<original statement>",
      "corrected_statement": "<corrected statement>",
      "statement_type": "before_error|error_statement|after_error",
      "changes_made": "<description of the specific change made to fix the deployment error>"
    }}
  ],
  "explanation": "<brief explanation of what specific error was fixed and how>"
}}

IMPORTANT:
- Fix ONLY the specific error type mentioned in the deployment error message
- If the same error pattern appears multiple times in the statement, fix ALL instances of that pattern
- DO NOT fix different types of issues - only the specific error type from the deployment error
- Keep before/after statements unchanged (set corrected_statement = original_statement)
- For error statement: Apply minimal fix to resolve the specific error type only
- Preserve all original names exactly as they appear
- Use source context to understand intended functionality but don't over-correct

- Return ALL three statements (before, error, after) in the corrected_statements array
- Use the EXACT statement numbers provided in the context:
  * Before Error: statement_number = {target_error_context.before_statement_number}
  * Error Statement: statement_number = {target_error_context.error_statement_number}
  * After Error: statement_number = {target_error_context.after_statement_number}
- **PRIMARY FOCUS: Fix ONLY the deployment error in the error statement**
- **CONTEXT STATEMENTS: Before/after statements are for context awareness only**
- **DO NOT MODIFY: Before/after statements - keep them exactly as original_statement**
- Only the error_statement correction will be applied to the deployed code
- Before/after statements provide business logic context but should remain unchanged
- For before/after statements: ALWAYS set corrected_statement equal to original_statement
- For error statement: Provide the complete alternative implementation that fixes the error
- Focus on fixing the specific deployment error while maintaining the same functionality"""
